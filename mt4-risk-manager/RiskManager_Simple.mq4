//+------------------------------------------------------------------+
//|                                         RiskManager_Simple.mq4 |
//|                                  风险管理插件 - 简化兼容版本      |
//+------------------------------------------------------------------+
#property copyright "Risk Management Tool - Simple Version"
#property version   "1.00"
#property strict

// 输入参数
input double InitialRiskAmount = 100.0; // 初始最大亏损金额
input color StopLossColor = clrRed;     // 止损线颜色
input color TakeProfitColor = clrGreen; // 止盈线颜色
input int PanelX = 10;                  // 面板X坐标
input int PanelY = 30;                  // 面板Y坐标

// 全局变量
double MaxRiskAmount = 100.0;
double currentPrice = 0;
double stopLossPrice = 0;
double takeProfitPrice = 0;
double calculatedLots = 0;
double riskRatio = 0;
double stopLossAmount = 0;
double takeProfitAmount = 0;
bool isLongPosition = true;

// 对象名称
string slLineName = "SL_Line";
string tpLineName = "TP_Line";
string panelPrefix = "RM_";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    MaxRiskAmount = InitialRiskAmount;
    
    // 获取当前价格并设置初始线条位置
    currentPrice = Bid;
    stopLossPrice = currentPrice - 50 * Point;
    takeProfitPrice = currentPrice + 100 * Point;

    // 创建止损止盈线
    CreateLines();
    
    // 创建简单面板
    CreateSimplePanel();

    // 初始计算
    CalculatePosition();
    UpdateDisplay();

    Print("风险管理工具初始化完成");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 删除对象
    ObjectDelete(slLineName);
    ObjectDelete(tpLineName);
    
    // 删除面板对象
    for(int i = ObjectsTotal() - 1; i >= 0; i--)
    {
        string objName = ObjectName(i);
        if(StringFind(objName, panelPrefix) == 0)
        {
            ObjectDelete(objName);
        }
    }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    currentPrice = Bid;
    
    // 检查线条位置变化
    CheckLinePositions();
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == panelPrefix + "BuyBtn")
        {
            isLongPosition = true;
            ExecuteTrade();
        }
        else if(sparam == panelPrefix + "SellBtn")
        {
            isLongPosition = false;
            ExecuteTrade();
        }
    }
    
    // 处理拖拽事件
    if(id == CHARTEVENT_OBJECT_DRAG)
    {
        if(sparam == slLineName || sparam == tpLineName)
        {
            CheckLinePositions();
            Print("线条被拖拽: ", sparam);
        }
    }
}

//+------------------------------------------------------------------+
//| 创建线条                                                          |
//+------------------------------------------------------------------+
void CreateLines()
{
    // 删除已存在的线条
    ObjectDelete(slLineName);
    ObjectDelete(tpLineName);
    
    // 创建止损线
    ObjectCreate(slLineName, OBJ_HLINE, 0, 0, stopLossPrice);
    ObjectSet(slLineName, OBJPROP_COLOR, StopLossColor);
    ObjectSet(slLineName, OBJPROP_WIDTH, 2);
    ObjectSet(slLineName, OBJPROP_SELECTABLE, true);
    ObjectSetText(slLineName, "止损线");
    
    // 创建止盈线
    ObjectCreate(tpLineName, OBJ_HLINE, 0, 0, takeProfitPrice);
    ObjectSet(tpLineName, OBJPROP_COLOR, TakeProfitColor);
    ObjectSet(tpLineName, OBJPROP_WIDTH, 2);
    ObjectSet(tpLineName, OBJPROP_SELECTABLE, true);
    ObjectSetText(tpLineName, "止盈线");
    
    Print("线条创建完成");
}

//+------------------------------------------------------------------+
//| 创建简单面板                                                      |
//+------------------------------------------------------------------+
void CreateSimplePanel()
{
    // 背景
    ObjectCreate(panelPrefix + "Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSet(panelPrefix + "Background", OBJPROP_XDISTANCE, PanelX);
    ObjectSet(panelPrefix + "Background", OBJPROP_YDISTANCE, PanelY);
    ObjectSet(panelPrefix + "Background", OBJPROP_XSIZE, 200);
    ObjectSet(panelPrefix + "Background", OBJPROP_YSIZE, 150);
    ObjectSet(panelPrefix + "Background", OBJPROP_BGCOLOR, clrLightGray);
    
    // 标题
    ObjectCreate(panelPrefix + "Title", OBJ_LABEL, 0, 0, 0);
    ObjectSet(panelPrefix + "Title", OBJPROP_XDISTANCE, PanelX + 10);
    ObjectSet(panelPrefix + "Title", OBJPROP_YDISTANCE, PanelY + 10);
    ObjectSetText(panelPrefix + "Title", "风险管理", 10, "Arial", clrBlack);
    
    // 显示标签
    ObjectCreate(panelPrefix + "LotsLabel", OBJ_LABEL, 0, 0, 0);
    ObjectSet(panelPrefix + "LotsLabel", OBJPROP_XDISTANCE, PanelX + 10);
    ObjectSet(panelPrefix + "LotsLabel", OBJPROP_YDISTANCE, PanelY + 30);
    ObjectSetText(panelPrefix + "LotsLabel", "手数: 0.00", 9, "Arial", clrBlue);
    
    ObjectCreate(panelPrefix + "RatioLabel", OBJ_LABEL, 0, 0, 0);
    ObjectSet(panelPrefix + "RatioLabel", OBJPROP_XDISTANCE, PanelX + 10);
    ObjectSet(panelPrefix + "RatioLabel", OBJPROP_YDISTANCE, PanelY + 50);
    ObjectSetText(panelPrefix + "RatioLabel", "盈亏比: 0:1", 9, "Arial", clrBlue);
    
    ObjectCreate(panelPrefix + "RiskLabel", OBJ_LABEL, 0, 0, 0);
    ObjectSet(panelPrefix + "RiskLabel", OBJPROP_XDISTANCE, PanelX + 10);
    ObjectSet(panelPrefix + "RiskLabel", OBJPROP_YDISTANCE, PanelY + 70);
    ObjectSetText(panelPrefix + "RiskLabel", "风险: $" + DoubleToString(MaxRiskAmount, 2), 9, "Arial", clrRed);
    
    // 交易按钮
    ObjectCreate(panelPrefix + "BuyBtn", OBJ_BUTTON, 0, 0, 0);
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_XDISTANCE, PanelX + 10);
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_YDISTANCE, PanelY + 100);
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_XSIZE, 70);
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_YSIZE, 25);
    ObjectSetText(panelPrefix + "BuyBtn", "买入");
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_BGCOLOR, clrLightGreen);
    
    ObjectCreate(panelPrefix + "SellBtn", OBJ_BUTTON, 0, 0, 0);
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_XDISTANCE, PanelX + 90);
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_YDISTANCE, PanelY + 100);
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_XSIZE, 70);
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_YSIZE, 25);
    ObjectSetText(panelPrefix + "SellBtn", "卖出");
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_BGCOLOR, clrLightPink);
}

//+------------------------------------------------------------------+
//| 检查线条位置                                                      |
//+------------------------------------------------------------------+
void CheckLinePositions()
{
    if(ObjectFind(slLineName) >= 0)
    {
        double newSL = ObjectGet(slLineName, OBJPROP_PRICE1);
        if(newSL > 0 && MathAbs(newSL - stopLossPrice) > Point/2)
        {
            stopLossPrice = newSL;
            CalculatePosition();
            UpdateDisplay();
        }
    }
    
    if(ObjectFind(tpLineName) >= 0)
    {
        double newTP = ObjectGet(tpLineName, OBJPROP_PRICE1);
        if(newTP > 0 && MathAbs(newTP - takeProfitPrice) > Point/2)
        {
            takeProfitPrice = newTP;
            CalculatePosition();
            UpdateDisplay();
        }
    }
}

//+------------------------------------------------------------------+
//| 计算仓位                                                          |
//+------------------------------------------------------------------+
void CalculatePosition()
{
    if(stopLossPrice <= 0 || takeProfitPrice <= 0 || currentPrice <= 0)
        return;

    double stopLossPoints = MathAbs(currentPrice - stopLossPrice) / Point;
    double takeProfitPoints = MathAbs(takeProfitPrice - currentPrice) / Point;
    
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    if(tickValue <= 0) tickValue = 1.0;

    if(stopLossPoints > 0)
    {
        calculatedLots = MaxRiskAmount / (stopLossPoints * tickValue);
        
        double minLot = MarketInfo(Symbol(), MODE_MINLOT);
        double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
        double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);
        
        calculatedLots = MathMax(minLot, MathMin(maxLot,
                        MathFloor(calculatedLots / lotStep) * lotStep));
        
        riskRatio = takeProfitPoints / stopLossPoints;
        stopLossAmount = calculatedLots * stopLossPoints * tickValue;
        takeProfitAmount = calculatedLots * takeProfitPoints * tickValue;
    }
    else
    {
        calculatedLots = 0;
        riskRatio = 0;
        stopLossAmount = 0;
        takeProfitAmount = 0;
    }
}

//+------------------------------------------------------------------+
//| 更新显示                                                          |
//+------------------------------------------------------------------+
void UpdateDisplay()
{
    ObjectSetText(panelPrefix + "LotsLabel", "手数: " + DoubleToString(calculatedLots, 2));
    ObjectSetText(panelPrefix + "RatioLabel", "盈亏比: " + DoubleToString(riskRatio, 2) + ":1");
    
    // 更新线条标签
    ObjectSetText(slLineName, "止损: " + DoubleToString(stopLossPrice, Digits));
    ObjectSetText(tpLineName, "止盈: " + DoubleToString(takeProfitPrice, Digits));
}

//+------------------------------------------------------------------+
//| 执行交易                                                          |
//+------------------------------------------------------------------+
void ExecuteTrade()
{
    if(calculatedLots <= 0)
    {
        Alert("手数计算错误！");
        return;
    }

    int orderType = isLongPosition ? OP_BUY : OP_SELL;
    double openPrice = isLongPosition ? Ask : Bid;

    int ticket = OrderSend(Symbol(), orderType, calculatedLots, openPrice, 3,
                          stopLossPrice, takeProfitPrice,
                          "风险管理工具", 0, 0,
                          isLongPosition ? clrGreen : clrRed);

    if(ticket > 0)
    {
        Alert("订单成功！手数: " + DoubleToString(calculatedLots, 2) + 
              ", 盈亏比: " + DoubleToString(riskRatio, 2) + ":1");
    }
    else
    {
        Alert("订单失败！错误: " + IntegerToString(GetLastError()));
    }
}
