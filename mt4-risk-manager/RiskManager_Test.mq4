//+------------------------------------------------------------------+
//|                                          RiskManager_Test.mq4 |
//|                                  拖拽功能测试版本                |
//+------------------------------------------------------------------+
#property copyright "Risk Management Tool - Test Version"
#property version   "1.00"
#property strict

// 全局变量
double stopLossPrice = 0;
double takeProfitPrice = 0;
string slLineName = "SL_Line_Test";
string tpLineName = "TP_Line_Test";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== 拖拽功能测试开始 ===");
    
    // 获取当前价格
    double currentPrice = Bid;
    stopLossPrice = currentPrice - 50 * Point;
    takeProfitPrice = currentPrice + 100 * Point;
    
    Print("当前价格: ", DoubleToString(currentPrice, Digits));
    Print("止损价格: ", DoubleToString(stopLossPrice, Digits));
    Print("止盈价格: ", DoubleToString(takeProfitPrice, Digits));
    
    // 创建测试线条
    CreateTestLines();
    
    // 启用所有图表事件
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);
    ChartSetInteger(0, CHART_EVENT_OBJECT_DRAG, true);
    
    Print("图表事件已启用");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    ObjectDelete(0, slLineName);
    ObjectDelete(0, tpLineName);
    Print("=== 拖拽功能测试结束 ===");
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    // 打印所有事件用于调试
    Print("图表事件 - ID: ", id, ", sparam: ", sparam);
    
    // 处理对象拖拽事件
    if(id == CHARTEVENT_OBJECT_DRAG)
    {
        Print("*** 检测到拖拽事件 *** 对象: ", sparam);
        
        if(sparam == slLineName)
        {
            double newSL = ObjectGetDouble(0, slLineName, OBJPROP_PRICE1);
            Print("止损线新价格: ", DoubleToString(newSL, Digits));
            stopLossPrice = newSL;
        }
        else if(sparam == tpLineName)
        {
            double newTP = ObjectGetDouble(0, tpLineName, OBJPROP_PRICE1);
            Print("止盈线新价格: ", DoubleToString(newTP, Digits));
            takeProfitPrice = newTP;
        }
    }
    
    // 处理鼠标移动事件
    if(id == CHARTEVENT_MOUSE_MOVE)
    {
        // 检查选中的对象
        int totalObjects = ObjectsTotal(0);
        for(int i = 0; i < totalObjects; i++)
        {
            string objName = ObjectName(0, i);
            if(ObjectGetInteger(0, objName, OBJPROP_SELECTED) > 0)
            {
                if(objName == slLineName || objName == tpLineName)
                {
                    Print("线条被选中: ", objName);
                    
                    if(objName == slLineName)
                    {
                        double newSL = ObjectGetDouble(0, slLineName, OBJPROP_PRICE1);
                        if(MathAbs(newSL - stopLossPrice) > Point/10)
                        {
                            stopLossPrice = newSL;
                            Print("鼠标移动更新止损: ", DoubleToString(newSL, Digits));
                        }
                    }
                    else if(objName == tpLineName)
                    {
                        double newTP = ObjectGetDouble(0, tpLineName, OBJPROP_PRICE1);
                        if(MathAbs(newTP - takeProfitPrice) > Point/10)
                        {
                            takeProfitPrice = newTP;
                            Print("鼠标移动更新止盈: ", DoubleToString(newTP, Digits));
                        }
                    }
                }
                break;
            }
        }
    }
    
    // 处理对象点击事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        Print("对象被点击: ", sparam);
    }
}

//+------------------------------------------------------------------+
//| 创建测试线条                                                      |
//+------------------------------------------------------------------+
void CreateTestLines()
{
    // 删除已存在的线条
    ObjectDelete(0, slLineName);
    ObjectDelete(0, tpLineName);
    
    // 创建止损线
    if(ObjectCreate(0, slLineName, OBJ_HLINE, 0, 0, stopLossPrice))
    {
        ObjectSetInteger(0, slLineName, OBJPROP_COLOR, clrRed);
        ObjectSetInteger(0, slLineName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, slLineName, OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, slLineName, OBJPROP_BACK, false);
        ObjectSetInteger(0, slLineName, OBJPROP_SELECTABLE, true);
        ObjectSetInteger(0, slLineName, OBJPROP_READONLY, false);
        ObjectSetInteger(0, slLineName, OBJPROP_HIDDEN, false);
        ObjectSetString(0, slLineName, OBJPROP_TEXT, "止损线测试");
        Print("止损线创建成功");
    }
    else
    {
        Print("止损线创建失败，错误: ", GetLastError());
    }
    
    // 创建止盈线
    if(ObjectCreate(0, tpLineName, OBJ_HLINE, 0, 0, takeProfitPrice))
    {
        ObjectSetInteger(0, tpLineName, OBJPROP_COLOR, clrGreen);
        ObjectSetInteger(0, tpLineName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, tpLineName, OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, tpLineName, OBJPROP_BACK, false);
        ObjectSetInteger(0, tpLineName, OBJPROP_SELECTABLE, true);
        ObjectSetInteger(0, tpLineName, OBJPROP_READONLY, false);
        ObjectSetInteger(0, tpLineName, OBJPROP_HIDDEN, false);
        ObjectSetString(0, tpLineName, OBJPROP_TEXT, "止盈线测试");
        Print("止盈线创建成功");
    }
    else
    {
        Print("止盈线创建失败，错误: ", GetLastError());
    }
    
    ChartRedraw(0);
}
