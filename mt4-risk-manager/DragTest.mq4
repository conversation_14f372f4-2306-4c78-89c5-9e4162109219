//+------------------------------------------------------------------+
//|                                                     DragTest.mq4 |
//|                                  水平线拖拽功能测试专用版本        |
//+------------------------------------------------------------------+
#property copyright "Drag Test"
#property version   "1.00"
#property strict

// 全局变量
string testLineName = "TestLine";
double lastPrice = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 删除已存在的线条
    ObjectDelete(testLineName);
    
    // 获取当前价格
    double currentPrice = Bid;
    
    // 创建测试线条
    if(ObjectCreate(testLineName, OBJ_HLINE, 0, 0, currentPrice))
    {
        // 设置线条属性
        ObjectSet(testLineName, OBJPROP_COLOR, clrRed);
        ObjectSet(testLineName, OBJPROP_WIDTH, 3);
        ObjectSet(testLineName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSet(testLineName, OBJPROP_SELECTABLE, true);
        ObjectSet(testLineName, OBJPROP_BACK, false);
        
        // 设置线条文本
        ObjectSetText(testLineName, "测试拖拽线 - 请拖拽我", 12, "Arial", clrRed);
        
        lastPrice = currentPrice;
        
        Print("测试线条创建成功，价格: ", DoubleToString(currentPrice, Digits));
        Print("请用鼠标直接拖拽红色线条进行测试");
    }
    else
    {
        Print("测试线条创建失败，错误代码: ", GetLastError());
    }
    
    // 启动定时器
    EventSetTimer(1);
    
    // 强制刷新
    WindowRedraw();
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    EventKillTimer();
    ObjectDelete(testLineName);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 空函数
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 检查线条位置
    if(ObjectFind(testLineName) >= 0)
    {
        double currentPrice = ObjectGet(testLineName, OBJPROP_PRICE1);
        
        if(currentPrice > 0 && MathAbs(currentPrice - lastPrice) > Point/10)
        {
            Print("*** 拖拽检测成功! ***");
            Print("线条从 ", DoubleToString(lastPrice, Digits), 
                  " 移动到 ", DoubleToString(currentPrice, Digits));
            Print("移动距离: ", DoubleToString(MathAbs(currentPrice - lastPrice), Digits), " 点");
            
            lastPrice = currentPrice;
            
            // 更新线条文本
            ObjectSetText(testLineName, 
                         "拖拽成功! 当前价格: " + DoubleToString(currentPrice, Digits), 
                         12, "Arial", clrRed);
        }
    }
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    // 打印所有事件用于调试
    Print("图表事件: ID=", id, ", lparam=", lparam, ", dparam=", dparam, ", sparam=", sparam);
    
    if(id == CHARTEVENT_OBJECT_DRAG)
    {
        Print("*** 检测到拖拽事件! 对象: ", sparam, " ***");
        
        if(sparam == testLineName)
        {
            double newPrice = ObjectGet(testLineName, OBJPROP_PRICE1);
            Print("拖拽事件 - 新价格: ", DoubleToString(newPrice, Digits));
            lastPrice = newPrice;
        }
    }
    
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        Print("对象点击事件: ", sparam);
    }
    
    if(id == CHARTEVENT_MOUSE_MOVE)
    {
        // 检查是否有对象被选中
        for(int i = 0; i < ObjectsTotal(); i++)
        {
            string objName = ObjectName(i);
            if(objName == testLineName)
            {
                // 检查是否被选中（这在旧版MQL4中可能不工作）
                // 但我们可以检查位置变化
                double currentPrice = ObjectGet(testLineName, OBJPROP_PRICE1);
                if(MathAbs(currentPrice - lastPrice) > Point/10)
                {
                    Print("鼠标移动时检测到位置变化");
                    lastPrice = currentPrice;
                }
                break;
            }
        }
    }
}
