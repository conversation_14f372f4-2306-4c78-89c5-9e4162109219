//+------------------------------------------------------------------+
//|                                                 RiskManager.mq4 |
//|                                  风险管理插件 - 自动手数计算器    |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Risk Management Tool"
#property version   "1.00"
#property strict

// MQL4 兼容性: 定义图表事件常量 (如果不存在)
#ifndef CHARTEVENT_OBJECT_DRAG
#define CHARTEVENT_OBJECT_DRAG 8
#endif

#ifndef CHARTEVENT_OBJECT_CLICK
#define CHARTEVENT_OBJECT_CLICK 1
#endif

#ifndef CHARTEVENT_OBJECT_ENDEDIT
#define CHARTEVENT_OBJECT_ENDEDIT 3
#endif

#ifndef CHARTEVENT_MOUSE_MOVE
#define CHARTEVENT_MOUSE_MOVE 4
#endif

// 图表属性常量
#ifndef CHART_EVENT_OBJECT_DRAG
#define CHART_EVENT_OBJECT_DRAG 1
#endif

// 输入参数
input double InitialRiskAmount = 100.0; // 初始最大亏损金额
input color StopLossColor = clrRed;     // 止损线颜色
input color TakeProfitColor = clrGreen; // 止盈线颜色
input int PanelX = 10;                  // 面板X坐标
input int PanelY = 30;                  // 面板Y坐标

// 全局变量
double MaxRiskAmount = 100.0;           // 当前最大亏损金额
double currentPrice = 0;
double stopLossPrice = 0;
double takeProfitPrice = 0;
double calculatedLots = 0;
double riskRatio = 0;
double stopLossAmount = 0;
double takeProfitAmount = 0;
bool isLongPosition = true;

// 对象名称
string slLineName = "SL_Line";
string tpLineName = "TP_Line";
string panelPrefix = "RM_";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化风险金额
    MaxRiskAmount = InitialRiskAmount;

    // 关键：启用所有必要的图表事件
    ChartSetInteger(0, CHART_EVENT_OBJECT_DRAG, true);
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);

    // 启用图表事件
    EventSetTimer(1);  // 每秒检查一次

    Print("图表事件已启用，拖拽功能已激活");

    // 创建用户界面
    CreatePanel();

    // 创建止损止盈线
    currentPrice = Bid;
    stopLossPrice = currentPrice - 50 * Point;
    takeProfitPrice = currentPrice + 100 * Point;

    CreateStopLossLine();
    CreateTakeProfitLine();

    // 初始计算
    CalculatePosition();
    UpdateDisplay();

    // 刷新图表
    ChartRedraw();

    // 验证线条是否创建成功
    if(ObjectFind(0, slLineName) >= 0)
        Print("止损线创建验证成功");
    else
        Print("止损线创建验证失败");

    if(ObjectFind(0, tpLineName) >= 0)
        Print("止盈线创建验证成功");
    else
        Print("止盈线创建验证失败");

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 停止定时器
    EventKillTimer();

    // 删除所有创建的对象
    ObjectsDeleteAll(0, panelPrefix);
    ObjectDelete(slLineName);
    ObjectDelete(tpLineName);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    currentPrice = Bid;
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 定期检查线条位置是否被手动移动
    if(ObjectFind(0, slLineName) >= 0)
    {
        double newSL = ObjectGetDouble(0, slLineName, OBJPROP_PRICE1);
        if(newSL > 0 && MathAbs(newSL - stopLossPrice) > Point/2)
        {
            stopLossPrice = newSL;
            CalculatePosition();
            UpdateDisplay();
            Print("定时器检测到止损线移动: ", DoubleToString(newSL, Digits));
        }
    }

    if(ObjectFind(0, tpLineName) >= 0)
    {
        double newTP = ObjectGetDouble(0, tpLineName, OBJPROP_PRICE1);
        if(newTP > 0 && MathAbs(newTP - takeProfitPrice) > Point/2)
        {
            takeProfitPrice = newTP;
            CalculatePosition();
            UpdateDisplay();
            Print("定时器检测到止盈线移动: ", DoubleToString(newTP, Digits));
        }
    }
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == panelPrefix + "BuyBtn")
        {
            isLongPosition = true;
            ExecuteTrade();
        }
        else if(sparam == panelPrefix + "SellBtn")
        {
            isLongPosition = false;
            ExecuteTrade();
        }
        else if(sparam == panelPrefix + "UpdateBtn")
        {
            // 更新最大风险金额
            string riskStr = ObjectGetString(0, panelPrefix + "RiskEdit", OBJPROP_TEXT);
            double newRisk = StringToDouble(riskStr);
            if(newRisk > 0)
            {
                MaxRiskAmount = newRisk;
                CalculatePosition();
                UpdateDisplay();
            }
        }
    }

    // 处理对象拖拽事件
    if(id == CHARTEVENT_OBJECT_DRAG)
    {
        Print("检测到对象拖拽事件，对象名称: ", sparam);

        if(sparam == slLineName)
        {
            double newSL = ObjectGetDouble(0, slLineName, OBJPROP_PRICE1);
            if(newSL > 0)
            {
                stopLossPrice = newSL;
                CalculatePosition();
                UpdateDisplay();
                Print("止损线被拖拽到: ", DoubleToString(newSL, Digits));
            }
        }
        else if(sparam == tpLineName)
        {
            double newTP = ObjectGetDouble(0, tpLineName, OBJPROP_PRICE1);
            if(newTP > 0)
            {
                takeProfitPrice = newTP;
                CalculatePosition();
                UpdateDisplay();
                Print("止盈线被拖拽到: ", DoubleToString(newTP, Digits));
            }
        }
    }

    // 处理对象结束编辑事件
    if(id == CHARTEVENT_OBJECT_ENDEDIT)
    {
        if(sparam == slLineName || sparam == tpLineName)
        {
            CalculatePosition();
            UpdateDisplay();
            ChartRedraw();
        }
    }

    // 处理鼠标移动事件（用于检测拖拽）
    if(id == CHARTEVENT_MOUSE_MOVE)
    {
        // 检查是否有对象被选中
        string selectedObj = "";
        int totalObjects = ObjectsTotal(0);
        for(int i = 0; i < totalObjects; i++)
        {
            string objName = ObjectName(0, i);
            if(ObjectGetInteger(0, objName, OBJPROP_SELECTED) > 0)
            {
                selectedObj = objName;
                break;
            }
        }

        // 如果选中的是我们的线条，实时更新位置
        if(selectedObj == slLineName)
        {
            double newSL = ObjectGetDouble(0, slLineName, OBJPROP_PRICE1);
            if(newSL > 0 && MathAbs(newSL - stopLossPrice) > Point/10)
            {
                stopLossPrice = newSL;
                CalculatePosition();
                UpdateDisplay();
            }
        }
        else if(selectedObj == tpLineName)
        {
            double newTP = ObjectGetDouble(0, tpLineName, OBJPROP_PRICE1);
            if(newTP > 0 && MathAbs(newTP - takeProfitPrice) > Point/10)
            {
                takeProfitPrice = newTP;
                CalculatePosition();
                UpdateDisplay();
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 创建控制面板                                                      |
//+------------------------------------------------------------------+
void CreatePanel()
{
    int yOffset = 0;
    
    // 背景面板
    ObjectCreate(panelPrefix + "Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSet(panelPrefix + "Background", OBJPROP_XDISTANCE, PanelX);
    ObjectSet(panelPrefix + "Background", OBJPROP_YDISTANCE, PanelY);
    ObjectSet(panelPrefix + "Background", OBJPROP_XSIZE, 250);
    ObjectSet(panelPrefix + "Background", OBJPROP_YSIZE, 200);
    ObjectSet(panelPrefix + "Background", OBJPROP_BGCOLOR, clrLightGray);
    ObjectSet(panelPrefix + "Background", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    
    // 标题
    yOffset += 10;
    ObjectCreate(panelPrefix + "Title", OBJ_LABEL, 0, 0, 0);
    ObjectSet(panelPrefix + "Title", OBJPROP_XDISTANCE, PanelX + 10);
    ObjectSet(panelPrefix + "Title", OBJPROP_YDISTANCE, PanelY + yOffset);
    ObjectSetText(panelPrefix + "Title", "风险管理工具", 12, "Arial", clrBlack);
    
    // 最大风险金额输入
    yOffset += 25;
    ObjectCreate(panelPrefix + "RiskLabel", OBJ_LABEL, 0, 0, 0);
    ObjectSet(panelPrefix + "RiskLabel", OBJPROP_XDISTANCE, PanelX + 10);
    ObjectSet(panelPrefix + "RiskLabel", OBJPROP_YDISTANCE, PanelY + yOffset);
    ObjectSetText(panelPrefix + "RiskLabel", "最大亏损:", 9, "Arial", clrBlack);

    ObjectCreate(panelPrefix + "RiskEdit", OBJ_EDIT, 0, 0, 0);
    ObjectSet(panelPrefix + "RiskEdit", OBJPROP_XDISTANCE, PanelX + 80);
    ObjectSet(panelPrefix + "RiskEdit", OBJPROP_YDISTANCE, PanelY + yOffset - 2);
    ObjectSet(panelPrefix + "RiskEdit", OBJPROP_XSIZE, 60);
    ObjectSet(panelPrefix + "RiskEdit", OBJPROP_YSIZE, 18);
    ObjectSetText(panelPrefix + "RiskEdit", DoubleToString(MaxRiskAmount, 2));

    ObjectCreate(panelPrefix + "UpdateBtn", OBJ_BUTTON, 0, 0, 0);
    ObjectSet(panelPrefix + "UpdateBtn", OBJPROP_XDISTANCE, PanelX + 150);
    ObjectSet(panelPrefix + "UpdateBtn", OBJPROP_YDISTANCE, PanelY + yOffset - 2);
    ObjectSet(panelPrefix + "UpdateBtn", OBJPROP_XSIZE, 50);
    ObjectSet(panelPrefix + "UpdateBtn", OBJPROP_YSIZE, 18);
    ObjectSetText(panelPrefix + "UpdateBtn", "更新");
    
    // 计算结果显示区域
    yOffset += 30;
    CreateDisplayLabels(yOffset);
    
    // 交易按钮
    yOffset += 80;
    ObjectCreate(panelPrefix + "BuyBtn", OBJ_BUTTON, 0, 0, 0);
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_XDISTANCE, PanelX + 20);
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_YDISTANCE, PanelY + yOffset);
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_XSIZE, 80);
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_YSIZE, 25);
    ObjectSetText(panelPrefix + "BuyBtn", "买入");
    ObjectSet(panelPrefix + "BuyBtn", OBJPROP_BGCOLOR, clrLightGreen);

    ObjectCreate(panelPrefix + "SellBtn", OBJ_BUTTON, 0, 0, 0);
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_XDISTANCE, PanelX + 120);
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_YDISTANCE, PanelY + yOffset);
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_XSIZE, 80);
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_YSIZE, 25);
    ObjectSetText(panelPrefix + "SellBtn", "卖出");
    ObjectSet(panelPrefix + "SellBtn", OBJPROP_BGCOLOR, clrLightPink);
}

//+------------------------------------------------------------------+
//| 创建显示标签                                                      |
//+------------------------------------------------------------------+
void CreateDisplayLabels(int startY)
{
    string labels[] = {"手数:", "盈亏比:", "止损金额:", "止盈金额:"};
    string values[] = {"LotsValue", "RatioValue", "SLAmountValue", "TPAmountValue"};
    
    for(int i = 0; i < 4; i++)
    {
        int yPos = startY + i * 18;
        
        // 标签
        ObjectCreate(panelPrefix + "Label" + IntegerToString(i), OBJ_LABEL, 0, 0, 0);
        ObjectSet(panelPrefix + "Label" + IntegerToString(i), OBJPROP_XDISTANCE, PanelX + 10);
        ObjectSet(panelPrefix + "Label" + IntegerToString(i), OBJPROP_YDISTANCE, PanelY + yPos);
        ObjectSetText(panelPrefix + "Label" + IntegerToString(i), labels[i], 9, "Arial", clrBlack);

        // 数值
        ObjectCreate(panelPrefix + values[i], OBJ_LABEL, 0, 0, 0);
        ObjectSet(panelPrefix + values[i], OBJPROP_XDISTANCE, PanelX + 80);
        ObjectSet(panelPrefix + values[i], OBJPROP_YDISTANCE, PanelY + yPos);
        ObjectSetText(panelPrefix + values[i], "0.00", 9, "Arial", clrBlue);
    }
}

//+------------------------------------------------------------------+
//| 创建止损线                                                        |
//+------------------------------------------------------------------+
void CreateStopLossLine()
{
    // 先删除已存在的线条
    ObjectDelete(slLineName);

    // 创建新的止损线
    if(ObjectCreate(0, slLineName, OBJ_HLINE, 0, 0, stopLossPrice))
    {
        ObjectSetInteger(0, slLineName, OBJPROP_COLOR, StopLossColor);
        ObjectSetInteger(0, slLineName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, slLineName, OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, slLineName, OBJPROP_BACK, false);
        ObjectSetInteger(0, slLineName, OBJPROP_SELECTABLE, true);
        ObjectSetInteger(0, slLineName, OBJPROP_READONLY, false);  // 允许编辑
        ObjectSetInteger(0, slLineName, OBJPROP_HIDDEN, false);    // 不隐藏
        ObjectSetString(0, slLineName, OBJPROP_TEXT, "止损线 - 可拖拽");
        Print("止损线创建成功，价格: ", DoubleToString(stopLossPrice, Digits));
    }
    else
    {
        Print("止损线创建失败，错误代码: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 创建止盈线                                                        |
//+------------------------------------------------------------------+
void CreateTakeProfitLine()
{
    // 先删除已存在的线条
    ObjectDelete(tpLineName);

    // 创建新的止盈线
    if(ObjectCreate(0, tpLineName, OBJ_HLINE, 0, 0, takeProfitPrice))
    {
        ObjectSetInteger(0, tpLineName, OBJPROP_COLOR, TakeProfitColor);
        ObjectSetInteger(0, tpLineName, OBJPROP_STYLE, STYLE_SOLID);
        ObjectSetInteger(0, tpLineName, OBJPROP_WIDTH, 2);
        ObjectSetInteger(0, tpLineName, OBJPROP_BACK, false);
        ObjectSetInteger(0, tpLineName, OBJPROP_SELECTABLE, true);
        ObjectSetInteger(0, tpLineName, OBJPROP_READONLY, false);  // 允许编辑
        ObjectSetInteger(0, tpLineName, OBJPROP_HIDDEN, false);    // 不隐藏
        ObjectSetString(0, tpLineName, OBJPROP_TEXT, "止盈线 - 可拖拽");
        Print("止盈线创建成功，价格: ", DoubleToString(takeProfitPrice, Digits));
    }
    else
    {
        Print("止盈线创建失败，错误代码: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| 计算仓位信息                                                      |
//+------------------------------------------------------------------+
void CalculatePosition()
{
    if(stopLossPrice <= 0 || takeProfitPrice <= 0 || currentPrice <= 0)
        return;

    // 计算点差
    double stopLossPoints = MathAbs(currentPrice - stopLossPrice) / Point;
    double takeProfitPoints = MathAbs(takeProfitPrice - currentPrice) / Point;

    // 计算点值
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    if(tickValue <= 0) tickValue = 1.0;

    // 计算手数
    if(stopLossPoints > 0)
    {
        calculatedLots = MaxRiskAmount / (stopLossPoints * tickValue);

        // 标准化手数
        double minLot = MarketInfo(Symbol(), MODE_MINLOT);
        double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
        double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

        calculatedLots = MathMax(minLot, MathMin(maxLot,
                        MathFloor(calculatedLots / lotStep) * lotStep));
    }
    else
    {
        calculatedLots = 0;
    }

    // 计算盈亏比
    if(stopLossPoints > 0)
        riskRatio = takeProfitPoints / stopLossPoints;
    else
        riskRatio = 0;

    // 计算金额
    stopLossAmount = calculatedLots * stopLossPoints * tickValue;
    takeProfitAmount = calculatedLots * takeProfitPoints * tickValue;
}

//+------------------------------------------------------------------+
//| 更新显示信息                                                      |
//+------------------------------------------------------------------+
void UpdateDisplay()
{
    ObjectSetText(panelPrefix + "LotsValue", DoubleToString(calculatedLots, 2));
    ObjectSetText(panelPrefix + "RatioValue", DoubleToString(riskRatio, 2) + ":1");
    ObjectSetText(panelPrefix + "SLAmountValue", DoubleToString(stopLossAmount, 2));
    ObjectSetText(panelPrefix + "TPAmountValue", DoubleToString(takeProfitAmount, 2));

    // 更新线条标签
    ObjectSetText(slLineName,
                 "止损: " + DoubleToString(stopLossPrice, Digits) +
                 " (亏损: " + DoubleToString(stopLossAmount, 2) + ")");

    ObjectSetText(tpLineName,
                 "止盈: " + DoubleToString(takeProfitPrice, Digits) +
                 " (盈利: " + DoubleToString(takeProfitAmount, 2) + ")");
}

//+------------------------------------------------------------------+
//| 执行交易                                                          |
//+------------------------------------------------------------------+
void ExecuteTrade()
{
    if(calculatedLots <= 0)
    {
        Alert("手数计算错误，无法开仓！");
        return;
    }

    int orderType = isLongPosition ? OP_BUY : OP_SELL;
    double openPrice = isLongPosition ? Ask : Bid;

    int ticket = OrderSend(Symbol(), orderType, calculatedLots, openPrice, 3,
                          stopLossPrice, takeProfitPrice,
                          "风险管理工具开仓", 0, 0,
                          isLongPosition ? clrGreen : clrRed);

    if(ticket > 0)
    {
        Alert("订单已提交！订单号: " + IntegerToString(ticket) +
              ", 手数: " + DoubleToString(calculatedLots, 2) +
              ", 盈亏比: " + DoubleToString(riskRatio, 2) + ":1");
    }
    else
    {
        Alert("订单提交失败！错误代码: " + IntegerToString(GetLastError()));
    }
}
