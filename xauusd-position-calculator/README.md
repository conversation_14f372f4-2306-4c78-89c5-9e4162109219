# XAUUSD 仓位计算器

一个专为XAUUSD（黄金）交易设计的仓位大小计算器，帮助交易者精确计算每笔交易的仓位大小。

## 功能特点

- 🎯 **精确计算**: 基于XAUUSD交易规则（0.01手 = 1美金/点波动）
- 💰 **风险管理**: 根据您设定的最大亏损金额计算合适的仓位
- 📊 **实时验证**: 智能验证交易方向和价格逻辑
- 📱 **响应式设计**: 支持桌面和移动设备
- ⚠️ **风险提醒**: 自动检测并提醒潜在的交易风险

## 使用方法

1. **打开计算器**: 在浏览器中打开 `index.html` 文件
2. **输入参数**:
   - 每手最大亏损金额（USD）
   - 入场价格（USD/oz）
   - 止损价格（USD/oz）
   - 交易方向（做多/做空）
3. **获取结果**: 点击"计算仓位大小"按钮
4. **查看建议**: 系统会显示建议的仓位大小和相关风险指标

## 计算公式

```
仓位大小(手) = 风险金额 ÷ 风险距离 ÷ 100
风险距离 = |入场价格 - 止损价格|
```

## 📊 计算示例

假设：
- 风险金额：$100
- 入场价格：$2050.00
- 止损价格：$2040.00
- 交易方向：做多

计算过程：
- 风险距离：|2050.00 - 2040.00| = $10.00
- 仓位大小：100 ÷ 10 ÷ 100 = 0.10手

计算结果：
- 建议仓位：0.10手
- 最大亏损：$100.00

## 交易规则

- **XAUUSD标准**: 0.01手 = 当黄金价格变动1美金时，盈亏1美金
- **做多交易**: 止损价格应低于入场价格
- **做空交易**: 止损价格应高于入场价格

## 风险提醒

- 建议单笔交易风险不超过账户资金的2-3%
- 止损距离过小可能被市场噪音触发
- 止损距离过大可能影响风险回报比
- 大仓位交易需要特别谨慎

## 文件结构

```
xauusd-position-calculator/
├── index.html          # 主页面
├── style.css           # 样式文件
├── script.js           # 计算逻辑
└── README.md           # 说明文档
```

## 技术特性

- **纯前端**: 无需服务器，本地即可运行
- **现代CSS**: 使用Grid布局和渐变效果
- **ES6 JavaScript**: 面向对象编程，代码结构清晰
- **输入验证**: 实时验证用户输入的合理性
- **错误处理**: 友好的错误提示和警告信息

## 快捷键

- `Ctrl + Enter`: 快速计算仓位大小

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 免责声明

本计算器仅供参考，不构成投资建议。交易有风险，投资需谨慎。请在充分了解市场风险的基础上进行交易决策。

## 更新日志

### v1.0.0 (2024-08-03)
- 初始版本发布
- 基础仓位计算功能
- 响应式界面设计
- 风险验证和提醒功能
